import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Layout } from "./components/Layout/Layout";
import { HomeZeroState } from "./screens/HomeZeroState/HomeZeroState";
import { ChatSessions } from "./screens/ChatSessions/ChatSessions";
import { NewSession } from "./screens/NewSession/NewSession";
import AgentsPage from "./screens/Agents";
import Executions from "./screens/Executions";
import IntegrationsPage from "./screens/Integrations";
import SettingsLayout from "./screens/SettingsLayout";
import ToolConfigurationAgent from "./screens/ToolConfigurationAgent";

function App() {
  return (
    <Router>
      <Routes>
        <Route
          path="/"
          element={
            <Layout title="Welcome, <PERSON> Smith!\" subtitle="Senior Platform Engineer">
              <HomeZeroState />
            </Layout>
          }
        />
        <Route
          path="/chat-sessions"
          element={
            <Layout title="My Chat Sessions\" subtitle="Manage and review your conversation history">
              <ChatSessions />
            </Layout>
          }
        />
        <Route
          path="/new-session"
          element={
            <Layout title="New Chat Session\" subtitle="Start a conversation with an AccelOS AI Agent">
              <NewSession />
            </Layout>
          }
        />
        {/* Placeholder routes for other navigation items */}
        <Route
          path="/agents"
          element={
            <Layout title="AI Agents\" subtitle="Automate your software engineering workflows">
              <AgentsPage />
            </Layout>
          }
        />
        <Route
          path="/agents/tool-configuration"
          element={
            <Layout title="Tool Configuration Agent\" subtitle="Build and configure your AI agent workflow">
              <ToolConfigurationAgent />
            </Layout>
          }
        />
        <Route
          path="/executions"
          element={
            <Layout title="Executions\" subtitle="Monitor your automation executions">
              <Executions />
            </Layout>
          }
        />
        <Route
          path="/knowledge-graph"
          element={
            <Layout title="Knowledge Graph\" subtitle="Explore your software knowledge">
              <div className="p-8 text-center text-white">
                <h2 className="text-2xl font-bold mb-4">Knowledge Graph</h2>
                <p className="text-gray-400">Coming soon...</p>
              </div>
            </Layout>
          }
        />
        <Route
          path="/scorecards"
          element={
            <Layout title="Scorecards\" subtitle="Assess software asset maturity">
              <div className="p-8 text-center text-white">
                <h2 className="text-2xl font-bold mb-4">Scorecards</h2>
                <p className="text-gray-400">Coming soon...</p>
              </div>
            </Layout>
          }
        />
        <Route path="/settings" element={<SettingsLayout><div className="p-8 text-center text-white"><h2 className="text-2xl font-bold mb-4">Account Settings</h2><p className="text-gray-400">Select an option from the left to manage your account.</p></div></SettingsLayout>} />
        <Route path="/settings/integrations" element={<SettingsLayout><IntegrationsPage /></SettingsLayout>} />
        <Route path="/settings/runners" element={<SettingsLayout><div className="p-8 text-center text-white"><h2 className="text-2xl font-bold mb-4">Runners</h2><p className="text-gray-400">Coming soon...</p></div></SettingsLayout>} />
        <Route
          path="/settings"
          element={
            <Layout title="Settings\" subtitle="Configure your preferences">
              <div className="p-8 text-center text-white">
                <h2 className="text-2xl font-bold mb-4">Settings</h2>
                <p className="text-gray-400">Coming soon...</p>
              </div>
            </Layout>
          }
        />
        <Route
          path="/help"
          element={
            <Layout title="Help\" subtitle="Get support and documentation">
              <div className="p-8 text-center text-white">
                <h2 className="text-2xl font-bold mb-4">Help</h2>
                <p className="text-gray-400">Coming soon...</p>
              </div>
            </Layout>
          }
        />
      </Routes>
    </Router>
  );
}

export default App;