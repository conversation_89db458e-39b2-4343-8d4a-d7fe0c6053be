import { Link, useLocation, useNavigate } from 'react-router-dom';
import { ChevronLeftIcon, ChevronRightIcon, GitBranchIcon, PlayIcon } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';

const navItems = [
  {
    label: 'Integrations',
    path: '/settings/integrations',
    icon: <GitBranchIcon className="w-5 h-5 mr-2" />,
  },
  {
    label: 'Runners',
    path: '/settings/runners',
    icon: <PlayIcon className="w-5 h-5 mr-2" />,
  },
];


interface AccountSettingsNavProps {
  isCollapsed: boolean;
  setIsCollapsed: (v: boolean | ((prev: boolean) => boolean)) => void;
}

export default function AccountSettingsNav({ isCollapsed, setIsCollapsed }: AccountSettingsNavProps) {
  const location = useLocation();
  const navigate = useNavigate();
  // Try to go back to where the user came from, fallback to home
  const from = location.state?.from || '/';

  return (
    <aside className={`${isCollapsed ? 'w-[60px]' : 'w-56'} bg-[#0e0c28] border-r border-[#181f3a] min-h-screen flex flex-col transition-all duration-200`}>
      {/* Logo + Collapse Button */}
      {/* Collapse/Expand Button - same structure as main nav */}
      <div className={`flex ${isCollapsed ? 'flex-col items-center' : 'justify-end'} w-full`} style={{ minHeight: 72 }}>
        <button
          className="z-50 mt-2 mb-2 w-7 h-7 flex items-center justify-center bg-[#23264a] border border-[#0f0d28] rounded-full shadow hover:bg-[#161a2a] transition-colors"
          onClick={() => setIsCollapsed((c) => !c)}
          title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isCollapsed ? <ChevronRightIcon className="w-4 h-4 text-[#9ea4aa]" /> : <ChevronLeftIcon className="w-4 h-4 text-[#9ea4aa]" />}
        </button>
      </div>
      {/* AccelOS Logo Bar */}
      <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-2.5 px-7'} py-5 border-b border-[#0f0d28] bg-gradient-to-b from-[rgba(118,120,237,0.05)] to-[rgba(118,120,237,0.05)] bg-[#0f0d28]`}>
        <div className="inline-flex items-center gap-3.5 mx-auto">
          <div className="relative w-10 h-10 bg-[url(/subtract.svg)] bg-[100%_100%]" />
          {!isCollapsed && (
            <div className="font-['Manrope',Helvetica] font-semibold text-[#f7f8f9] text-lg leading-[22.3px]">
              AccelOS
            </div>
          )}
        </div>
      </div>
      {/* Back Link */}
      <button
        onClick={() => navigate(from)}
        className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-2.5 pl-7 pr-3'} py-1.5 cursor-pointer border-b border-[#181f3a] bg-[#0e0c28]`}
        style={{ outline: 'none', boxShadow: 'none' }}
      >
        <div className="inline-flex items-center gap-2.5 p-2.5 rounded-lg text-[#9ea4aa]">
          <ChevronLeftIcon className="w-5 h-5" />
        </div>
        {!isCollapsed && (
          <div className="flex-1 font-['Figtree',Helvetica] font-normal text-sm leading-[17px] text-[#b3b9be]">Back</div>
        )}
      </button>
      {/* Settings Nav */}
      <nav className={`flex flex-col gap-2 flex-1 mt-6 ${isCollapsed ? 'px-0 items-center' : 'px-2'}`}>
        {navItems.map(item => {
          const isActive = location.pathname === item.path;
          return (
            <Link
              key={item.path}
              to={item.path}
              className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-2.5 pl-7 pr-3'} py-1.5 cursor-pointer`}
            >
              <div className={`inline-flex items-center gap-2.5 p-2.5 rounded-lg ${isActive ? 'text-[#7678ed]' : 'text-[#9ea4aa]'}`}>
                <GitBranchIcon className="w-5 h-5" />
              </div>
              {!isCollapsed && (
                <div className={`flex-1 font-['Figtree',Helvetica] font-normal text-sm leading-[17px] ${isActive ? 'text-[#7678ed]' : 'text-[#b3b9be]'}`}>{item.label}</div>
              )}
            </Link>
          );
        })}
      </nav>
      {/* User Profile at bottom */}
      <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-4 px-7'} py-5 border-t border-[#0f0d28] bg-[#0f0d28] mt-auto`}>
        <Avatar className="w-[41.5px] h-[41.5px]">
          <AvatarImage src="/profile.svg" alt="John Smith" />
          <AvatarFallback>JS</AvatarFallback>
        </Avatar>
        {!isCollapsed && (
          <div className="flex-1 font-['Figtree',Helvetica] font-normal text-[#c9cdd2] text-base leading-[19.8px]">
            John Smith
          </div>
        )}
      </div>
    </aside>
  );
}