import {
  GitBranchIcon,
  HelpCircleIcon,
  HomeIcon,
  LightbulbIcon,
  MessageSquareIcon,
  PieChartIcon,
  SettingsIcon,
  BotIcon,
  ClipboardCheckIcon,
  PlayIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "lucide-react";
import React, { useState } from "react";
import { useLocation, Link } from "react-router-dom";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../ui/avatar";

// Navigation menu data
const navigationItems = [
  {
    title: null,
    items: [
      {
        icon: <HomeIcon className="w-6 h-6" />,
        label: "My Home",
        path: "/",
      },
      {
        icon: <MessageSquareIcon className="w-6 h-6" />,
        label: "My Chat Sessions",
        path: "/chat-sessions",
      },
    ],
  },
  {
    title: "AUTOMATE SDLC",
    items: [
      {
        icon: <BotIcon className="w-6 h-6" />,
        label: "AI Agents",
        path: "/agents",
      },
      {
        icon: <PieChartIcon className="w-6 h-6" />,
        label: "Executions",
        path: "/executions",
      },
    ],
  },
  {
    title: "EXPLORE",
    items: [
      {
        icon: <LightbulbIcon className="w-6 h-6" />,
        label: "Eng Knowledge Graph",
        path: "/knowledge-graph",
      },
      {
        icon: <ClipboardCheckIcon className="w-6 h-6" />,
        label: "Scorecards",
        path: "/scorecards",
      },
    ],
  },

  {
    title: null,
    items: [], // First empty section
  },

  {
    title: null,
    items: [], // Second empty section
  },

  {
    title: null,
    items: [
      {
        icon: <SettingsIcon className="w-6 h-6" />,
        label: "Account Settings",
        path: "/settings",
      },
      {
        icon: <HelpCircleIcon className="w-6 h-6" />,
        label: "Help",
        path: "/help",
      },
    ],
  },
];

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
}

export const Layout = ({ children, title, subtitle }: LayoutProps): JSX.Element => {
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  return (
    <div className="bg-[#020816] flex flex-row justify-center w-full min-h-screen h-screen">
      <div className="bg-[#020816] overflow-hidden w-full max-w-[1728px] min-h-screen h-screen relative">
        {/* Header */}
        <header className={`absolute w-full h-[82px] top-0 ${isCollapsed ? 'left-[60px]' : 'left-[250px]'} bg-[#0e0c28] border-b-[1.5px] border-[#0f0d28] transition-all duration-200`}>
          <div className="flex flex-col items-start gap-1 p-5">
            <h1 className="font-['Manrope',Helvetica] font-bold text-[#f7f8f9] text-lg leading-[22.3px]">
              {title || "Welcome, John Smith!"}
            </h1>
            <p className="font-['Figtree',Helvetica] font-normal text-[#c9cdd2] text-sm leading-[19.6px]">
              {subtitle || "Senior Platform Engineer"}
            </p>
          </div>
        </header>

        {/* Sidebar */}
        <aside className={`fixed ${isCollapsed ? 'w-[60px]' : 'w-[250px]'} h-[100vh] top-0 left-0 transition-all duration-200 bg-[#0e0c28] z-40 flex flex-col`}>
          {/* Collapse/Expand Button */}
          <div className={`flex ${isCollapsed ? 'flex-col items-center' : 'justify-end'} w-full`} style={{ minHeight: 72 }}>
            <button
              className="z-50 mt-2 mb-2 w-7 h-7 flex items-center justify-center bg-[#23264a] border border-[#0f0d28] rounded-full shadow hover:bg-[#161a2a] transition-colors"
              onClick={() => setIsCollapsed((c) => !c)}
              title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            >
              {isCollapsed ? (
                <ChevronRightIcon className="w-4 h-4 text-[#9ea4aa]" />
              ) : (
                <ChevronLeftIcon className="w-4 h-4 text-[#9ea4aa]" />
              )}
            </button>
          </div>
          {/* Logo */}
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-2.5 px-7'} py-5 border-b border-[#0f0d28] bg-gradient-to-b from-[rgba(118,120,237,0.05)] to-[rgba(118,120,237,0.05)], bg-[#0f0d28]`}> 
            <div className="inline-flex items-center gap-3.5">
              <div className="relative w-10 h-10 bg-[url(/subtract.svg)] bg-[100%_100%]" />
              {!isCollapsed && (
                <div className="font-['Manrope',Helvetica] font-semibold text-[#f7f8f9] text-lg leading-[22.3px]">
                  AccelOS
                </div>
              )}
            </div>
          </div>
          {/* Navigation */}
          <nav className="flex flex-col flex-1">
            {navigationItems.map((section, sectionIndex) => (
              <div key={sectionIndex} className="flex flex-col gap-1 mb-2">
                {!isCollapsed && section.title && (
                  <div className="px-7 py-0">
                    <h3 className="font-['Figtree',Helvetica] font-semibold text-[#9ea4aa80] text-[13px] leading-[18.2px]">
                      {section.title}
                    </h3>
                  </div>
                )}
                {section.items.map((item, itemIndex) => {
                  const isActive = location.pathname === item.path;
                  return (
                    <Link
                      key={itemIndex}
                      to={item.path}
                      className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-2.5 pl-7 pr-3'} py-1.5 cursor-pointer`}
                    >
                      <div className={`inline-flex items-center gap-2.5 p-2.5 rounded-lg ${isActive ? 'text-[#7678ed]' : 'text-[#9ea4aa]'}`}>{item.icon}</div>
                      {!isCollapsed && (
                        <div className={`flex-1 font-['Figtree',Helvetica] font-normal text-sm leading-[17px] ${isActive ? 'text-[#7678ed]' : 'text-[#b3b9be]'}`}>{item.label}</div>
                      )}
                    </Link>
                  );
                })}
              </div>
            ))}
          </nav>
          {/* User Profile */}
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-4 px-7'} py-5 border-t border-[#0f0d28] bg-[#0f0d28]`}>
            <Avatar className="w-[41.5px] h-[41.5px]">
              <AvatarImage src="/profile.svg" alt="John Smith" />
              <AvatarFallback>JS</AvatarFallback>
            </Avatar>
            {!isCollapsed && (
              <div className="flex-1 font-['Figtree',Helvetica] font-normal text-[#c9cdd2] text-base leading-[19.8px]">
                John Smith
              </div>
            )}
          </div>
        </aside>

        {/* Main Content */}
        <main className={`absolute top-[82px] ${isCollapsed ? 'left-[60px]' : 'left-[250px]'} right-0 bottom-0 overflow-auto transition-all duration-200`}>
          {children}
        </main>
      </div>
    </div>
  );
};