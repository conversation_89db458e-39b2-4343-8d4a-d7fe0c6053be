import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  PlayIcon,
  SaveIcon,
  SettingsIcon,
  TrashIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  LinkIcon
} from 'lucide-react';
import { Card, CardContent } from '../../components/ui/card';

interface FlowNode {
  id: string;
  type: 'url' | 'calculator' | 'agent' | 'chat-input' | 'chat-output' | 'note';
  position: { x: number; y: number };
  data: {
    title: string;
    description: string;
    config: Record<string, any>;
    collapsed?: boolean;
  };
}

interface Connection {
  id: string;
  source: string;
  target: string;
  sourceHandle: string;
  targetHandle: string;
  style?: {
    color?: string;
    strokeWidth?: number;
    strokeDasharray?: string;
    arrowStyle?: 'default' | 'large' | 'filled';
  };
  label?: string;
  selected?: boolean;
}

const nodeTypes = {
  url: {
    title: 'URL',
    description: 'Load and retrieve data from specified URLs',
    color: 'bg-[#7678ed]',
    icon: '🌐'
  },
  calculator: {
    title: 'Calculator',
    description: 'Perform basic arithmetic operations',
    color: 'bg-[#7678ed]',
    icon: '🧮'
  },
  agent: {
    title: 'Agent',
    description: 'Define the agent\'s instructions',
    color: 'bg-[#7678ed]',
    icon: '🤖'
  },
  'chat-input': {
    title: 'Chat Input',
    description: 'Receive input from user',
    color: 'bg-[#9ea4aa]',
    icon: '💬'
  },
  'chat-output': {
    title: 'Chat Output',
    description: 'Send output to user',
    color: 'bg-cyan-600',
    icon: '📤'
  },
  note: {
    title: 'Note',
    description: 'Documentation and notes',
    color: 'bg-amber-600',
    icon: '📝'
  }
};

const ToolConfigurationAgent: React.FC = () => {
  const [nodes, setNodes] = useState<FlowNode[]>([
    {
      id: 'readme-note',
      type: 'note',
      position: { x: 600, y: 50 },
      data: {
        title: 'README',
        description: 'Project documentation and quick start guide',
        collapsed: false,
        config: {
          content: `Run an Agent with URL and Calculator tools available for its use. The Agent decides which tool to use to solve a problem.

**Quick start**
1. Add your OpenAI API key to the Agent.
2. Open the Playground and chat with the Agent. Request some information about a recipe, and then ask it to add two numbers together. In the responses, the Agent will use different tools to solve different problems.

**Next steps**
Connect more tools to the Agent to create your perfect assistant.

For more, see the Langflow docs.`
        }
      }
    },
    {
      id: 'url-1',
      type: 'url',
      position: { x: 100, y: 100 },
      data: {
        title: 'URL',
        description: 'Load and retrieve data from specified URLs',
        collapsed: false,
        config: {
          outputFormat: 'Text',
          separator: '',
          cleanExtraWhitespace: true,
          urls: []
        }
      }
    },
    {
      id: 'calculator-1',
      type: 'calculator',
      position: { x: 100, y: 400 },
      data: {
        title: 'Calculator',
        description: 'Perform basic arithmetic operations',
        collapsed: false,
        config: {
          expression: '',
          receivingInput: ''
        }
      }
    },
    {
      id: 'agent-1',
      type: 'agent',
      position: { x: 500, y: 250 },
      data: {
        title: 'Agent',
        description: 'Define the agent\'s instructions',
        collapsed: false,
        config: {
          modelProvider: 'OpenAI',
          modelName: 'gpt-4o',
          apiKey: '',
          instructions: 'You are a helpful assistant that can...',
          tools: [],
          input: '',
          response: ''
        }
      }
    },
    {
      id: 'chat-input-1',
      type: 'chat-input',
      position: { x: 100, y: 700 },
      data: {
        title: 'Chat Input',
        description: 'Receive input from user',
        collapsed: false,
        config: {}
      }
    },
    {
      id: 'chat-output-1',
      type: 'chat-output',
      position: { x: 800, y: 250 },
      data: {
        title: 'Chat Output',
        description: 'Send output to user',
        collapsed: false,
        config: {}
      }
    }
  ]);

  const [connections, setConnections] = useState<Connection[]>([
    {
      id: 'conn-1',
      source: 'url-1',
      target: 'agent-1',
      sourceHandle: 'output',
      targetHandle: 'input',
      style: {
        color: '#06b6d4',
        strokeWidth: 2,
        arrowStyle: 'default'
      }
    },
    {
      id: 'conn-2',
      source: 'calculator-1',
      target: 'agent-1',
      sourceHandle: 'output',
      targetHandle: 'tools',
      style: {
        color: '#8b5cf6',
        strokeWidth: 3,
        arrowStyle: 'large'
      },
      label: 'Tools'
    },
    {
      id: 'conn-3',
      source: 'agent-1',
      target: 'chat-output-1',
      sourceHandle: 'output',
      targetHandle: 'input',
      style: {
        color: '#10b981',
        strokeWidth: 2,
        arrowStyle: 'default'
      }
    },
    {
      id: 'conn-4',
      source: 'chat-input-1',
      target: 'agent-1',
      sourceHandle: 'output',
      targetHandle: 'input',
      style: {
        color: '#f59e0b',
        strokeWidth: 2,
        strokeDasharray: '8,4',
        arrowStyle: 'default'
      },
      label: 'Input'
    }
  ]);

  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [selectedConnection, setSelectedConnection] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStart, setConnectionStart] = useState<{ nodeId: string; handle: string } | null>(null);
  const [tempConnection, setTempConnection] = useState<{ x: number; y: number } | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Delete selected connection with Delete or Backspace key
      if ((e.key === 'Delete' || e.key === 'Backspace') && selectedConnection) {
        e.preventDefault();
        deleteSelectedConnection();
      }
      // Escape key to deselect
      if (e.key === 'Escape') {
        setSelectedConnection(null);
        setSelectedNode(null);
        if (isConnecting) {
          setIsConnecting(false);
          setConnectionStart(null);
          setTempConnection(null);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedConnection, isConnecting]);

  const handleNodeMouseDown = useCallback((e: React.MouseEvent, nodeId: string) => {
    // Check if the click is on a button or interactive element
    const target = e.target as HTMLElement;
    const isButton = target.tagName === 'BUTTON' || target.closest('button');
    const isInput = target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA';

    // Don't start dragging if clicking on interactive elements
    if (isButton || isInput) {
      return;
    }

    e.preventDefault();
    setSelectedNode(nodeId);
    setIsDragging(true);

    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      const rect = e.currentTarget.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  }, [nodes]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging && selectedNode && canvasRef.current) {
      const canvasRect = canvasRef.current.getBoundingClientRect();
      const newX = e.clientX - canvasRect.left - dragOffset.x;
      const newY = e.clientY - canvasRect.top - dragOffset.y;

      setNodes(prev => prev.map(node => 
        node.id === selectedNode 
          ? { ...node, position: { x: Math.max(0, newX), y: Math.max(0, newY) } }
          : node
      ));
    }

    // Update temporary connection line while connecting
    if (isConnecting && canvasRef.current) {
      const canvasRect = canvasRef.current.getBoundingClientRect();
      setTempConnection({
        x: e.clientX - canvasRect.left,
        y: e.clientY - canvasRect.top
      });
    }
  }, [isDragging, selectedNode, dragOffset, isConnecting]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragOffset({ x: 0, y: 0 });
  }, []);

  const addNode = (type: keyof typeof nodeTypes) => {
    const newNode: FlowNode = {
      id: `${type}-${Date.now()}`,
      type,
      position: { x: 200, y: 200 },
      data: {
        title: nodeTypes[type].title,
        description: nodeTypes[type].description,
        collapsed: false,
        config: {}
      }
    };
    setNodes(prev => [...prev, newNode]);
  };

  const deleteNode = (nodeId: string) => {
    setNodes(prev => prev.filter(n => n.id !== nodeId));
    setConnections(prev => prev.filter(c => c.source !== nodeId && c.target !== nodeId));
    if (selectedNode === nodeId) {
      setSelectedNode(null);
    }
  };

  const toggleNodeCollapse = (nodeId: string) => {
    setNodes(prev => prev.map(node => 
      node.id === nodeId 
        ? { ...node, data: { ...node.data, collapsed: !node.data.collapsed } }
        : node
    ));
  };

  const handleConnectionStart = (e: React.MouseEvent, nodeId: string, handle: string) => {
    e.stopPropagation();
    setIsConnecting(true);
    setConnectionStart({ nodeId, handle });

    // Initialize temp connection position to mouse position relative to canvas
    if (canvasRef.current) {
      const canvasRect = canvasRef.current.getBoundingClientRect();
      setTempConnection({
        x: e.clientX - canvasRect.left,
        y: e.clientY - canvasRect.top
      });
    }
  };

  const handleConnectionEnd = (e: React.MouseEvent, nodeId: string, handle: string) => {
    e.stopPropagation();

    if (connectionStart && connectionStart.nodeId !== nodeId) {
      const newConnection: Connection = {
        id: `conn-${Date.now()}`,
        source: connectionStart.nodeId,
        target: nodeId,
        sourceHandle: connectionStart.handle,
        targetHandle: handle,
        style: {
          color: '#06b6d4',
          strokeWidth: 2,
          arrowStyle: 'default'
        }
      };
      setConnections(prev => [...prev, newConnection]);

      // Auto-select the newly created connection
      setSelectedConnection(newConnection.id);
    }

    // Reset connection state
    setIsConnecting(false);
    setConnectionStart(null);
    setTempConnection(null);
  };

  const handleCanvasClick = useCallback((_e: React.MouseEvent) => {
    // Cancel connection if clicking on empty canvas
    if (isConnecting) {
      setIsConnecting(false);
      setConnectionStart(null);
      setTempConnection(null);
    }
    // Deselect connection and node when clicking on empty canvas
    setSelectedConnection(null);
    setSelectedNode(null);
  }, [isConnecting]);

  // Enhanced curved path generation similar to Google Slides
  const generateCurvedPath = (sourceX: number, sourceY: number, targetX: number, targetY: number) => {
    const dx = targetX - sourceX;
    const dy = targetY - sourceY;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // Adaptive curve strength based on distance and direction
    const curveStrength = Math.min(distance * 0.4, 150);

    // Determine if connection is going left-to-right or right-to-left
    const isForward = dx > 0;

    // Smart control points for natural curves
    let cp1x, cp1y, cp2x, cp2y;

    if (isForward) {
      // Forward connection: curve outward
      cp1x = sourceX + curveStrength;
      cp1y = sourceY;
      cp2x = targetX - curveStrength;
      cp2y = targetY;
    } else {
      // Backward connection: create S-curve to avoid overlap
      const verticalOffset = Math.abs(dy) > 50 ? 0 : 80;

      cp1x = sourceX + curveStrength * 0.7;
      cp1y = sourceY + (dy > 0 ? -verticalOffset : verticalOffset);
      cp2x = targetX - curveStrength * 0.7;
      cp2y = targetY + (dy > 0 ? verticalOffset : -verticalOffset);
    }

    return `M ${sourceX} ${sourceY} C ${cp1x} ${cp1y} ${cp2x} ${cp2y} ${targetX} ${targetY}`;
  };

  // Delete selected connection
  const deleteSelectedConnection = () => {
    if (selectedConnection) {
      setConnections(prev => prev.filter(c => c.id !== selectedConnection));
      setSelectedConnection(null);
    }
  };

  // Handle connection click for selection
  const handleConnectionClick = (connectionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedConnection(connectionId);
    setSelectedNode(null);
  };

  const getNodeConnectionPoint = (node: FlowNode, handle: 'input' | 'output') => {
    const nodeWidth = node.type === 'note' ? 320 : 256;
    const nodeHeight = 100; // approximate center
    
    if (handle === 'output') {
      return {
        x: node.position.x + nodeWidth,
        y: node.position.y + nodeHeight
      };
    } else {
      return {
        x: node.position.x,
        y: node.position.y + nodeHeight
      };
    }
  };

  const NodeComponent: React.FC<{ node: FlowNode; toggleNodeCollapse: (nodeId: string) => void }> = ({ node, toggleNodeCollapse }) => {
    const nodeType = nodeTypes[node.type];
    const isSelected = selectedNode === node.id;

    return (
      <div
        className={`absolute cursor-move select-none ${isSelected ? 'z-20' : 'z-10'}`}
        style={{ 
          left: node.position.x, 
          top: node.position.y,
          transform: isDragging && isSelected ? 'scale(1.05)' : 'scale(1)',
          transition: isDragging && isSelected ? 'none' : 'transform 0.2s ease'
        }}
        onMouseDown={(e) => handleNodeMouseDown(e, node.id)}
      >
        <Card className={`${node.type === 'note' ? 'w-80' : 'w-64'} bg-[#020816] border transition-all font-['Manrope',sans-serif] ${
          isSelected ? 'border-cyan-400 ring-2 ring-cyan-300/80' : 'border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80'
        }`}>
          <CardContent className="p-4">
            {/* Node Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className={`w-6 h-6 rounded ${nodeType.color} flex items-center justify-center text-white text-sm`}>
                  {nodeType.icon}
                </div>
                <span className="text-[#f7f8f9] font-semibold text-sm">{node.data.title}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleNodeCollapse(node.id);
                  }}
                  className="p-1 hover:bg-[#0e0c28] rounded transition-colors"
                >
                  {node.data.collapsed ? (
                    <ChevronDownIcon className="w-4 h-4 text-[#9ea4aa]" />
                  ) : (
                    <ChevronUpIcon className="w-4 h-4 text-[#9ea4aa]" />
                  )}
                </button>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  deleteNode(node.id);
                }}
                className="p-1 hover:bg-red-600/20 rounded text-red-400 hover:text-red-300 transition-colors"
              >
                <TrashIcon className="w-4 h-4" />
              </button>
            </div>

            {/* Collapsible Content */}
            <div className={`transition-all duration-300 overflow-hidden ${node.data.collapsed ? 'max-h-0 opacity-0' : 'max-h-[1000px] opacity-100'}`}>
              <div className="space-y-3">
                {/* Node Description */}
                <p className="text-[#c9cdd2] text-xs mb-4 font-['Figtree',sans-serif]">{node.data.description}</p>

                {/* Note Content */}
                {node.type === 'note' && (
                  <div className="bg-[#0e0c28] border border-[#0f0d28] rounded-lg p-4">
                    <div className="text-[#f7f8f9] text-sm font-['Figtree',sans-serif] whitespace-pre-line">
                      {node.data.config.content}
                    </div>
                  </div>
                )}

                {/* URL Node Configuration */}
                {node.type === 'url' && (
                  <>
                    <div>
                      <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">Output Format</label>
                      <select className="w-full bg-[#0e0c28] border border-[#0f0d28] rounded px-2 py-1 text-[#f7f8f9] text-sm font-['Figtree',sans-serif] focus:outline-none focus:ring-2 focus:ring-cyan-400/50">
                        <option>Text</option>
                        <option>JSON</option>
                        <option>HTML</option>
                      </select>
                    </div>
                    <div>
                      <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">Separator</label>
                      <input 
                        type="text" 
                        className="w-full bg-[#0e0c28] border border-[#0f0d28] rounded px-2 py-1 text-[#f7f8f9] text-sm font-['Figtree',sans-serif] focus:outline-none focus:ring-2 focus:ring-cyan-400/50"
                        placeholder="Enter separator..."
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" defaultChecked />
                      <label className="text-[#c9cdd2] text-xs font-['Figtree',sans-serif]">Clean Extra Whitespace</label>
                    </div>
                    <div>
                      <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">URLs</label>
                      <div className="text-[#9ea4aa] text-xs font-['Figtree',sans-serif]">Receiving input</div>
                    </div>
                  </>
                )}

                {/* Calculator Node Configuration */}
                {node.type === 'calculator' && (
                  <div>
                    <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">Expression</label>
                    <div className="text-[#9ea4aa] text-xs font-['Figtree',sans-serif]">Receiving input</div>
                  </div>
                )}

                {/* Agent Node Configuration */}
                {node.type === 'agent' && (
                  <>
                    <div>
                      <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">Model Provider</label>
                      <select className="w-full bg-[#0e0c28] border border-[#0f0d28] rounded px-2 py-1 text-[#f7f8f9] text-sm font-['Figtree',sans-serif] focus:outline-none focus:ring-2 focus:ring-cyan-400/50">
                        <option>OpenAI</option>
                        <option>Anthropic</option>
                        <option>Google</option>
                      </select>
                    </div>
                    <div>
                      <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">Model Name</label>
                      <select className="w-full bg-[#0e0c28] border border-[#0f0d28] rounded px-2 py-1 text-[#f7f8f9] text-sm font-['Figtree',sans-serif] focus:outline-none focus:ring-2 focus:ring-cyan-400/50">
                        <option>gpt-4o</option>
                        <option>gpt-3.5-turbo</option>
                        <option>claude-3-sonnet</option>
                      </select>
                    </div>
                    <div>
                      <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">OpenAI API Key</label>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                        <div className="flex-1 h-6 bg-[#0e0c28] border border-[#0f0d28] rounded"></div>
                        <LinkIcon className="w-4 h-4 text-[#9ea4aa]" />
                      </div>
                    </div>
                    <div>
                      <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">Agent Instructions</label>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                        <div className="text-[#9ea4aa] text-xs font-['Figtree',sans-serif]">You are a helpful assistant that can...</div>
                      </div>
                    </div>
                    <div>
                      <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">Tools</label>
                      <div className="text-[#9ea4aa] text-xs font-['Figtree',sans-serif]">No tools selected</div>
                    </div>
                    <div>
                      <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">Input</label>
                      <div className="text-[#9ea4aa] text-xs font-['Figtree',sans-serif]">Receiving input</div>
                    </div>
                    <div>
                      <label className="text-[#c9cdd2] text-xs block mb-1 font-['Figtree',sans-serif]">Response</label>
                      <div className="text-[#9ea4aa] text-xs font-['Figtree',sans-serif]">Output</div>
                    </div>
                  </>
                )}

                {/* Chat Input Node */}
                {node.type === 'chat-input' && (
                  <div className="text-center py-4">
                    <div className="text-[#c9cdd2] text-sm font-['Figtree',sans-serif]">💬 Chat Input</div>
                  </div>
                )}

                {/* Chat Output Node */}
                {node.type === 'chat-output' && (
                  <div className="text-center py-4">
                    <div className="text-[#c9cdd2] text-sm font-['Figtree',sans-serif]">📤 Chat Output</div>
                  </div>
                )}
              </div>
            </div>

            {/* Connection Points - Only show for non-note nodes */}
            {node.type !== 'note' && (
              <>
                {/* Input connection point (left side) */}
                <div 
                  className={`absolute -left-2 top-1/2 w-4 h-4 rounded-full border-2 border-[#020816] cursor-pointer transition-all z-30 ${
                    isConnecting ? 'bg-cyan-400 hover:bg-cyan-300 scale-110' : 'bg-[#23264a] hover:bg-cyan-400'
                  }`}
                  onClick={(e) => handleConnectionEnd(e, node.id, 'input')}
                  title="Input connection point"
                ></div>
                
                {/* Output connection point (right side) */}
                <div 
                  className={`absolute -right-2 top-1/2 w-4 h-4 rounded-full border-2 border-[#020816] cursor-pointer transition-all z-30 ${
                    connectionStart?.nodeId === node.id ? 'bg-cyan-400 scale-110' : 'bg-[#23264a] hover:bg-cyan-400'
                  }`}
                  onClick={(e) => handleConnectionStart(e, node.id, 'output')}
                  title="Output connection point - click to start connection"
                ></div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className="h-full bg-[#020816] text-white flex font-['Manrope',sans-serif]">
      {/* Sidebar */}
      <div className="w-80 bg-[#0e0c28] border-r border-[#0f0d28] p-6 overflow-y-auto">
        {/* Add Node Section */}
        <div className="mb-6">
          <h3 className="text-[#f7f8f9] font-bold text-lg mb-4">Add Components</h3>
          <div className="space-y-3">
            {Object.entries(nodeTypes).map(([type, config]) => (
              <button
                key={type}
                onClick={() => addNode(type as keyof typeof nodeTypes)}
                className="w-full flex items-center gap-3 p-3 bg-[#020816] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg transition-all"
              >
                <div className={`w-8 h-8 rounded ${config.color} flex items-center justify-center text-white`}>
                  {config.icon}
                </div>
                <div className="text-left">
                  <div className="text-[#f7f8f9] font-semibold text-sm">{config.title}</div>
                  <div className="text-[#c9cdd2] text-xs font-['Figtree',sans-serif]">{config.description}</div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Connection Instructions */}
        {isConnecting && (
          <Card className="bg-cyan-900/20 border border-cyan-600/50 mb-6">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-cyan-400">🔗</span>
                <span className="text-cyan-300 font-semibold">Creating Connection</span>
              </div>
              <p className="text-cyan-200 text-sm">Click on the left circle of another node to complete the connection, or click anywhere else to cancel.</p>
            </CardContent>
          </Card>
        )}

        {/* Selected Connection Controls */}
        {selectedConnection && (
          <Card className="bg-purple-900/20 border border-purple-600/50 mb-6">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <span className="text-purple-400">⚡</span>
                <span className="text-purple-300 font-semibold">Connection Selected</span>
              </div>
              <div className="space-y-2">
                <button
                  onClick={deleteSelectedConnection}
                  className="w-full flex items-center gap-2 p-2 bg-red-600/20 hover:bg-red-600/30 border border-red-600/50 rounded text-red-300 hover:text-red-200 transition-colors text-sm"
                >
                  <TrashIcon className="w-4 h-4" />
                  Delete Connection
                </button>
                <p className="text-purple-200 text-xs">Press Delete key or click above to remove this connection.</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* API Key Notice */}
        <Card className="bg-orange-900/20 border border-orange-600/50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-orange-400">💡</span>
              <span className="text-orange-300 font-semibold">Add your OpenAI API key here 👆</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Canvas */}
      <div className="flex-1 relative overflow-hidden">
        {/* Toolbar */}
        <div className="absolute top-6 left-6 z-30 flex items-center gap-3">
          <button className="p-3 bg-[#020816] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg text-[#f7f8f9] transition-all">
            <PlayIcon className="w-5 h-5" />
          </button>
          <button className="p-3 bg-[#020816] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg text-[#f7f8f9] transition-all">
            <SaveIcon className="w-5 h-5" />
          </button>
          <button className="p-3 bg-[#020816] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg text-[#f7f8f9] transition-all">
            <SettingsIcon className="w-5 h-5" />
          </button>
          {/* Global collapse/expand chevron */}
          <button
            className="p-3 bg-[#181f3a] hover:bg-[#23264a] border border-cyan-400/40 rounded-full shadow transition-colors flex items-center gap-2"
            title={nodes.length > 0 && nodes.every((node) => node.data.collapsed) ? 'Expand all nodes' : 'Collapse all nodes'}
            onClick={() => setNodes(prev => prev.map(node => ({
              ...node,
              data: { ...node.data, collapsed: !nodes.every((node) => node.data.collapsed) }
            })))}
          >
            {nodes.length > 0 && nodes.every((node) => node.data.collapsed) ? (
              <ChevronDownIcon className="w-5 h-5 text-[#9ea4aa]" />
            ) : (
              <ChevronUpIcon className="w-5 h-5 text-[#9ea4aa]" />
            )}
            <span className="hidden md:inline text-xs text-[#c9cdd2] font-semibold">
              {nodes.length > 0 && nodes.every((node) => node.data.collapsed) ? 'Expand All' : 'Collapse All'}
            </span>
          </button>
        </div>
        {/* Canvas with background grid */}
        <div
          ref={canvasRef}
          className="w-full h-full"
          style={{
            backgroundImage: `
              radial-gradient(circle, #0f0d28 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px'
          }}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onClick={handleCanvasClick}
        >
          {/* Render Connections */}
          <svg className="absolute inset-0 pointer-events-none" style={{ zIndex: 25 }}>
            <defs>
              {/* Default arrow marker */}
              <marker
                id="arrowhead"
                markerWidth="12"
                markerHeight="8"
                refX="11"
                refY="4"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <polygon
                  points="0 0, 12 4, 0 8"
                  fill="#06b6d4"
                />
              </marker>

              {/* Selected arrow marker */}
              <marker
                id="arrowhead-selected"
                markerWidth="12"
                markerHeight="8"
                refX="11"
                refY="4"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <polygon
                  points="0 0, 12 4, 0 8"
                  fill="#22d3ee"
                />
              </marker>

              {/* Large arrow marker */}
              <marker
                id="arrowhead-large"
                markerWidth="16"
                markerHeight="10"
                refX="15"
                refY="5"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <polygon
                  points="0 0, 16 5, 0 10"
                  fill="#06b6d4"
                />
              </marker>

              {/* Temporary connection arrow */}
              <marker
                id="arrowhead-temp"
                markerWidth="12"
                markerHeight="8"
                refX="11"
                refY="4"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <polygon
                  points="0 0, 12 4, 0 8"
                  fill="#fbbf24"
                />
              </marker>

              {/* Glow filter for selected connections */}
              <filter id="glow">
                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>
            
            {/* Existing connections */}
            {connections.map(connection => {
              const sourceNode = nodes.find(n => n.id === connection.source);
              const targetNode = nodes.find(n => n.id === connection.target);

              if (!sourceNode || !targetNode) return null;

              const sourcePoint = getNodeConnectionPoint(sourceNode, 'output');
              const targetPoint = getNodeConnectionPoint(targetNode, 'input');
              const path = generateCurvedPath(sourcePoint.x, sourcePoint.y, targetPoint.x, targetPoint.y);

              const isSelected = selectedConnection === connection.id;
              const strokeColor = connection.style?.color || (isSelected ? '#22d3ee' : '#06b6d4');
              const strokeWidth = connection.style?.strokeWidth || (isSelected ? 3 : 2);
              const arrowMarker = isSelected ? 'url(#arrowhead-selected)' :
                                 connection.style?.arrowStyle === 'large' ? 'url(#arrowhead-large)' :
                                 'url(#arrowhead)';

              return (
                <g key={connection.id}>
                  {/* Invisible wider path for easier clicking */}
                  <path
                    d={path}
                    stroke="transparent"
                    strokeWidth="12"
                    fill="none"
                    style={{ cursor: 'pointer', pointerEvents: 'all' }}
                    onClick={(e) => handleConnectionClick(connection.id, e)}
                  />

                  {/* Shadow/glow effect */}
                  <path
                    d={path}
                    stroke={strokeColor}
                    strokeWidth={strokeWidth + 2}
                    fill="none"
                    opacity={isSelected ? "0.4" : "0.2"}
                    filter="blur(2px)"
                    style={{ pointerEvents: 'none' }}
                  />

                  {/* Main path */}
                  <path
                    d={path}
                    stroke={strokeColor}
                    strokeWidth={strokeWidth}
                    fill="none"
                    strokeDasharray={connection.style?.strokeDasharray}
                    markerEnd={arrowMarker}
                    filter={isSelected ? "url(#glow)" : undefined}
                    className={`transition-all duration-200 ${isSelected ? 'drop-shadow-lg' : 'drop-shadow-sm'}`}
                    style={{ pointerEvents: 'none' }}
                  />

                  {/* Connection label */}
                  {connection.label && (
                    <text
                      x={(sourcePoint.x + targetPoint.x) / 2}
                      y={(sourcePoint.y + targetPoint.y) / 2 - 10}
                      fill={strokeColor}
                      fontSize="12"
                      textAnchor="middle"
                      className="font-['Figtree',sans-serif] select-none"
                      style={{ pointerEvents: 'none' }}
                    >
                      {connection.label}
                    </text>
                  )}
                </g>
              );
            })}

            {/* Temporary connection line while connecting */}
            {isConnecting && connectionStart && tempConnection && (
              (() => {
                const sourceNode = nodes.find(n => n.id === connectionStart.nodeId);
                if (!sourceNode) return null;

                const sourcePoint = getNodeConnectionPoint(sourceNode, connectionStart.handle as 'input' | 'output');
                const path = generateCurvedPath(sourcePoint.x, sourcePoint.y, tempConnection.x, tempConnection.y);

                return (
                  <g>
                    {/* Animated glow effect */}
                    <path
                      d={path}
                      stroke="#fbbf24"
                      strokeWidth="4"
                      fill="none"
                      opacity="0.6"
                      filter="blur(2px)"
                    />
                    {/* Main temporary path */}
                    <path
                      d={path}
                      stroke="#fbbf24"
                      strokeWidth="2"
                      fill="none"
                      strokeDasharray="5,5"
                      markerEnd="url(#arrowhead-temp)"
                      className="animate-pulse"
                    />
                  </g>
                );
              })()
            )}
          </svg>

          {/* Render Nodes */}
          {nodes.map(node => (
            <NodeComponent key={node.id} node={node} toggleNodeCollapse={toggleNodeCollapse} />
          ))}
        </div>
      </div>
    </div>
  );
}

export default ToolConfigurationAgent;