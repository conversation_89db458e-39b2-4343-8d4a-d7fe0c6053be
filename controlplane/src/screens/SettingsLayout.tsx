import React from 'react';
import AccountSettingsNav from '../components/AccountSettingsNav';

import { useState } from 'react';

export default function SettingsLayout({ children }: { children: React.ReactNode }) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  return (
    <div className="flex min-h-screen">
      <AccountSettingsNav isCollapsed={isCollapsed} setIsCollapsed={setIsCollapsed} />
      <main className="flex-1 bg-[#020816] p-8">{children}</main>
    </div>
  );
}
