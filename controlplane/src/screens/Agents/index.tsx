import React from 'react';
import { Heart<PERSON><PERSON>, <PERSON><PERSON>he<PERSON>, Wrench } from 'lucide-react';
import { Link } from 'react-router-dom';

export const agents = [
  {
    name: 'Tool Configuration Agent',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    icon: <Wrench className="w-10 h-10 text-white opacity-90" />,
    gradient: 'from-blue-900/70 to-blue-600/70',
    path: '/agents/tool-configuration'
  },
  {
    name: 'Scorecards Agent',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    icon: <HeartPulse className="w-10 h-10 text-white opacity-90" />,
    gradient: 'from-purple-900/70 to-pink-600/70',
    path: '/agents/scorecards'
  },
  {
    name: 'Knowledge Graph Agent',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    icon: <ShieldCheck className="w-10 h-10 text-white opacity-90" />,
    gradient: 'from-blue-900/70 to-blue-600/70',
    path: '/agents/knowledge-graph'
  }
];

const buildYourOwn = {
  name: 'Build Your Own AI Agent',
  description:
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
  gradient: 'from-orange-600/70 to-green-500/70',
  path: '/agents/build-your-own'
};

const PolygonBackground = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none">
    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
      <div className="relative w-[209px] h-[241px]">
        <img className="absolute w-[184px] h-[213px] top-3.5 left-3" alt="Polygon" src="/polygon-5.svg" />
        <img className="absolute w-40 h-[185px] top-7 left-6" alt="Polygon" src="/polygon-6.svg" />
        <img className="absolute w-[136px] h-[157px] top-[42px] left-9" alt="Polygon" src="/polygon-2.svg" />
        <img className="absolute w-[111px] h-[129px] top-14 left-[49px]" alt="Polygon" src="/polygon-8.svg" />
      </div>
    </div>
  </div>
);

const AgentsPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-[#0a0f1c] px-8 py-12 font-['Manrope',sans-serif]">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
        {agents.map((agent) => (
          <Link
            key={agent.name}
            to={agent.path}
            className="relative rounded-2xl overflow-hidden shadow-lg bg-[#020816] h-[415px] flex flex-col justify-end cursor-pointer border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 transition-all"
          >
            {/* Blurred gradient overlay */}
            <div className={`absolute inset-0 bg-gradient-to-br ${agent.gradient} opacity-20 blur-[75px]`}></div>
            {/* Polygon SVGs */}
            <PolygonBackground />
            {/* Icon */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
              {agent.icon}
            </div>
            {/* Content */}
            <div className="relative z-10 px-6 pb-12">
              <h2 className="font-semibold text-[#f7f8f9] text-xl leading-[24.8px] mb-3">
                {agent.name}
              </h2>
              <p className="font-normal text-[#c9cdd2] text-[13.5px] leading-[18.9px]">
                {agent.description}
              </p>
            </div>
          </Link>
        ))}
      </div>
      <Link
        to={buildYourOwn.path}
        className="relative rounded-2xl overflow-hidden shadow-lg bg-[#020816] h-[180px] md:col-span-3 flex flex-col justify-end cursor-pointer border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 transition-all block"
      >
        {/* Blurred gradient overlay */}
        <div className={`absolute inset-0 bg-gradient-to-br ${buildYourOwn.gradient} opacity-20 blur-[75px]`}></div>
        {/* Polygon SVGs */}
        <PolygonBackground />
        {/* Content */}
        <div className="relative z-10 px-6 pb-8">
          <h2 className="font-semibold text-[#f7f8f9] text-xl leading-[24.8px] mb-3">
            {buildYourOwn.name}
          </h2>
          <p className="font-normal text-[#c9cdd2] text-[13.5px] leading-[18.9px]">
            {buildYourOwn.description}
          </p>
        </div>
      </Link>
    </div>
  );
};

export default AgentsPage;