import {
  ShieldCheckIcon,
  WrenchIcon,
} from "lucide-react";
import React from "react";
import { Card, CardContent } from "../../components/ui/card";
import { Link } from "react-router-dom";

// Card data
const actionCards = [
  {
    title: "Automate SDLC",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    icon: (
      <div className="w-16 h-16 flex items-center justify-center">
        <img className="w-[54px] h-[50px]" alt="Union" src="/union.svg" />
      </div>
    ),
    bgClass: "bg-gradient-to-br from-purple-900/70 to-pink-600/70",
  },
  {
    title: "Explore Knowledge Graph",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    icon: <ShieldCheckIcon className="w-16 h-16" />,
    bgClass: "bg-gradient-to-br from-blue-900/70 to-blue-600/70",
  },
  {
    title: "Configure Integrations",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    icon: <WrenchIcon className="w-16 h-16" />,
    bgClass: "bg-gradient-to-br from-orange-600/70 to-green-500/70",
  },
];

export const HomeZeroState = (): JSX.Element => {
  return (
    <div className="p-8">
      <h2 className="text-center mt-[135px] mb-[58px] text-white font-['Manrope',Helvetica] font-medium text-[28px] leading-[34.7px]">
        What Would You Like To Do Today?
      </h2>

      {/* Cards */}
      <div className="flex flex-wrap justify-center gap-6">
        {actionCards.map((card, index) => {
          let linkTo = "/new-session";
          if (card.title === "Explore Knowledge Graph") {
            linkTo = "/knowledge-graph";
          } else if (card.title === "Configure Integrations") {
            linkTo = "/settings/integrations";
          }
          return (
            <Link key={index} to={linkTo}>
              <Card
                className="w-[311px] h-[415px] bg-[#020816] rounded-2xl overflow-hidden relative cursor-pointer border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 transition-all"
              >
                <CardContent className="p-0 h-full">
                  {/* Background Effects */}
                  <div
                    className={`absolute inset-0 ${card.bgClass} opacity-20 blur-[75px]`}
                  ></div>

                  {/* Polygon Decorations */}
                  <div className="absolute inset-0 overflow-hidden">
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <div className="relative w-[209px] h-[241px]">
                        <img
                          className="absolute w-[184px] h-[213px] top-3.5 left-3"
                          alt="Polygon"
                          src="/polygon-5.svg"
                        />
                        <img
                          className="absolute w-40 h-[185px] top-7 left-6"
                          alt="Polygon"
                          src="/polygon-6.svg"
                        />
                        <img
                          className="absolute w-[136px] h-[157px] top-[42px] left-9"
                          alt="Polygon"
                          src="/polygon-2.svg"
                        />
                        <img
                          className="absolute w-[111px] h-[129px] top-14 left-[49px]"
                          alt="Polygon"
                          src="/polygon-8.svg"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Icon */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white">
                    {card.icon}
                  </div>

                  {/* Content */}
                  <div className="absolute bottom-12 left-6 right-6">
                    <h3 className="font-['Manrope',Helvetica] font-semibold text-[#f7f8f9] text-xl leading-[24.8px] mb-3">
                      {card.title}
                    </h3>
                    <p className="font-['Figtree',Helvetica] font-normal text-[#c9cdd2] text-[13.5px] leading-[18.9px]">
                      {card.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>
    </div>
  );
};