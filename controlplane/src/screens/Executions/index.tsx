import { Card } from "../../components/ui/card";
import { agents } from "../Agents/index";
import { MessageSquareIcon, ServerIcon, SlackIcon, GithubIcon, ZapIcon } from "lucide-react";

const agentNames = agents.map(a => a.name);
const dummyExecutions = Array.from({ length: 10 }).map((_, idx) => {
  let status = ["active", "completed", "waiting-on-input"][idx % 3];
  return {
    id: `exec-${String(idx + 1).padStart(3, '0')}`,
    agent: agentNames[idx % agentNames.length],
    triggeredVia: ["Chat", "MCP Server", "Slack Webhook", "GitHub Webhook", "Other"][idx % 5],
    status,
    startTime: `2025-06-08 1${idx}:00:00`,
    endTime: status === "completed" || status === "error" ? `2025-06-08 1${idx}:20:00` : "",
  };
}).concat([
  {
    id: "exec-011",
    agent: agent<PERSON><PERSON>s[0],
    triggeredVia: "Chat",
    status: "error",
    startTime: "2025-06-08 10:00:00",
    endTime: "2025-06-08 10:20:00"
  },
  {
    id: "exec-012",
    agent: agentNames[1],
    triggeredVia: "Slack Webhook",
    status: "error",
    startTime: "2025-06-08 11:00:00",
    endTime: "2025-06-08 11:20:00"
  }
]);

const statusColors: Record<string, string> = {
  active: "bg-green-500/20 text-green-400 border-green-500/30",
  completed: "bg-blue-500/20 text-blue-400 border-blue-500/30",
  'waiting-on-input': "bg-yellow-500/20 text-yellow-400 border-yellow-500/30",
  error: "bg-red-500/20 text-red-400 border-red-500/30",
};

export default function Executions() {
  return (
    <div className="p-6 font-['Manrope',sans-serif]">
      <Card className="overflow-x-auto bg-[#0e0c28] border border-cyan-400/40 shadow-lg">
        <table className="min-w-full divide-y divide-cyan-900 text-[#f7f8f9]">
          <thead className="bg-[#0e0c28]">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">Execution ID</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">Agent Name</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">Triggered Via</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">Start Time</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">End Time</th>
            </tr>
          </thead>
          <tbody className="bg-[#020816] divide-y divide-[#0f0d28]">
            {dummyExecutions.map((exec) => (
              <tr key={exec.id} className="hover:bg-[#181f3a] transition-colors">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-[#f7f8f9]">{exec.id}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-[#f7f8f9]">{exec.agent}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-[#c9cdd2] flex items-center gap-2">
                  {exec.triggeredVia === "Chat" && <MessageSquareIcon className="w-4 h-4 text-cyan-300" />}
                  {exec.triggeredVia === "MCP Server" && <ServerIcon className="w-4 h-4 text-cyan-300" />}
                  {exec.triggeredVia === "Slack Webhook" && <SlackIcon className="w-4 h-4 text-cyan-300" />}
                  {exec.triggeredVia === "GitHub Webhook" && <GithubIcon className="w-4 h-4 text-cyan-300" />}
                  {exec.triggeredVia === "Other" && <ZapIcon className="w-4 h-4 text-cyan-300" />}
                  <span>{exec.triggeredVia}</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full border ${statusColors[exec.status]}`}>{
                    exec.status === 'waiting-on-input' ? 'Waiting on Input'
                    : exec.status.charAt(0).toUpperCase() + exec.status.slice(1)
                  }</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-[#c9cdd2]">{exec.startTime}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-[#c9cdd2]">{(exec.status === 'completed' || exec.status === 'error') ? exec.endTime : ''}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </Card>
    </div>
  );
}
